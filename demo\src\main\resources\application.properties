spring.application.name=Smart Scheduler

# H2 In-Memory Database Configuration (for testing)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# H2 Console (for debugging - accessible at http://localhost:8080/h2-console)
spring.h2.console.enabled=true
