package com.smart.scheduler;

package com.yourname.smarthealthhome.entity;

// JPA Annotations for ORM (mapping Java to DB)
import jakarta.persistence.*;

// Lombok to reduce boilerplate code (auto generate getters/setters)
import lombok.*;

@Entity // Marks this class as a database entity
@Getter // Generates getters for all fields
@Setter // Generates setters for all fields
@NoArgsConstructor // Creates no-args constructor
@AllArgsConstructor // Creates all-args constructor
@Table(name = "users") // Sets the table name to 'users'
public class User {

    @Id // Primary key
    @GeneratedValue(strategy = GenerationType.IDENTITY) // Auto-increment
    private Long id;

    private String name;

    @Column(unique = true) // Unique constraint on email
    private String email;

    private String password;
}

